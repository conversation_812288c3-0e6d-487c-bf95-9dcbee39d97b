<template>
    <view class="container">
        <!-- 批次状态筛选 -->
        <view class="tabs-container">
            <!-- 使用 u-tabs 组件 -->
            <u-tabs v-if="false" :list="batchStatusTabs" :current="currentTabIndex" @change="onTabChange"
                :scrollable="false" lineColor="#007AFF" :activeStyle="{ color: '#007AFF' }"
                :inactiveStyle="{ color: '#666666' }"></u-tabs>

            <!-- 备用方案：自定义选项卡 -->
            <view class="custom-tabs">
                <view v-for="(tab, index) in batchStatusTabs" :key="index"
                    :class="['custom-tab', currentTabIndex === index ? 'active' : '']" @tap="onTabChange(index)">
                    {{ tab.name }}
                </view>
            </view>
        </view>

        <!-- 批次列表 -->
        <view class="media-list-container">
            <!-- 批次卡片列表项 -->
            <MediaCard v-for="(batch, index) in filteredBatches" :key="index" :item="formatBatchItem(batch)"
                @click="viewBatchDetail" />

            <!-- 空状态 -->
            <u-empty v-if="filteredBatches.length === 0" mode="list" :text="`暂无${getCurrentStatusLabel()}批次`"
                iconSize="120" textSize="16" marginTop="100">
                <u-button type="primary" text="创建批次" @click="createNewBatch" size="normal" shape="round"></u-button>
            </u-empty>
        </view>
    </view>
</template>

<script>

import { queryBatches } from "@/api/batch.js";
import mediaCommonMixin from "@/mixins/media-common.js";
import MediaCard from '@/components/MediaCard.vue';

export default {
    mixins: [mediaCommonMixin],
    components: {
        MediaCard
    },
    data () {
        return {
            batchList: [],
            batchStatusTabs: [
                { name: '全部', value: 'all' },
                { name: '进行中', value: 'active' },
                { name: '未开始', value: 'pending' },
                { name: '已结束', value: 'ended' }
            ],
            currentBatchStatus: 'all',
            currentTabIndex: 0
        }
    },
    computed: {
        filteredBatches () {
            let result = this.batchList;

            // 按状态筛选
            if (this.currentBatchStatus !== 'all') {
                result = result.filter(batch => batch.status === this.currentBatchStatus);
            }

            return result;
        }
    },
    created () {
        this.loadAllBatches();
    },
    methods: {
        async loadAllBatches () {
            try {
                this.showLoading("加载批次中...");

                const response = await queryBatches({
                    page: 1,
                    pageSize: 1000
                });

                if (response.success && response.data) {
                    this.batchList = response.data.items.map(batch => {
                        console.log('批次数据:', batch);
                        console.log('videoCoverUrl:', batch.videoCoverUrl);
                        const coverUrl = this.buildCompleteFileUrl(batch.videoCoverUrl);
                        console.log('处理后的封面URL:', coverUrl);

                        return {
                            id: batch.id,
                            batchId: batch.batchId,
                            title: batch.name || batch.title, // 优先使用name字段作为批次名称
                            description: batch.description,
                            startTime: batch.startTime,
                            endTime: batch.endTime,
                            status: this.calculateBatchStatus(batch),
                            participants: batch.participants || 0,
                            totalReward: batch.totalReward || 0,
                            videos: batch.videoId ? [{
                                id: batch.videoId,
                                title: batch.videoTitle,
                                cover: coverUrl,
                                duration: batch.videoDuration
                            }] : [],
                            createdAt: batch.createdAt
                        };
                    });
                } else {
                    console.error('获取批次列表失败:', response.message);
                    this.showError(response.message || "获取批次列表失败");
                    this.batchList = [];
                }

                this.hideLoading();
            } catch (error) {
                console.error('加载批次列表失败:', error);
                this.hideLoading();
                this.showError("加载失败");
                this.batchList = [];
            }
        },

        // 计算批次状态
        calculateBatchStatus (batch) {
            const now = new Date();
            const startTime = new Date(batch.startTime);
            const endTime = new Date(batch.endTime);

            if (now < startTime) {
                return 'pending'; // 未开始
            } else if (now > endTime) {
                return 'ended'; // 已结束
            } else {
                return 'active'; // 进行中
            }
        },

        // 选项卡切换
        onTabChange (index) {
            this.currentTabIndex = index;
            this.currentBatchStatus = this.batchStatusTabs[index].value;
        },

        // 格式化日期范围
        formatDateRange (startTime, endTime) {
            const start = new Date(startTime);
            const end = new Date(endTime);

            const formatDate = (date) => {
                const month = (date.getMonth() + 1).toString().padStart(2, '0');
                const day = date.getDate().toString().padStart(2, '0');
                const hour = date.getHours().toString().padStart(2, '0');
                const minute = date.getMinutes().toString().padStart(2, '0');
                return `${month}-${day} ${hour}:${minute}`;
            };

            return `${formatDate(start)} ~ ${formatDate(end)}`;
        },

        // 获取批次状态文本
        getBatchStatusText (batch) {
            const statusMap = {
                'pending': '未开始',
                'active': '进行中',
                'ended': '已结束',
                'paused': '已暂停'
            };
            return statusMap[batch.status] || '未知';
        },

        // 获取批次状态对应的 uview-plus 标签类型
        getBatchStatusType (batch) {
            // 首先检查是否已过期
            const now = new Date();
            const endTime = new Date(batch.endTime);
            const startTime = new Date(batch.startTime);

            if (now > endTime) {
                return 'error'; // 已结束
            }

            if (now < startTime) {
                return 'warning'; // 未开始
            }

            // 其他状态
            if (batch.status === 'paused') return 'warning';
            return 'success'; // 进行中
        },

        viewBatchDetail (item) {
            // 添加详细的调试日志
            console.log('viewBatchDetail 接收到的 item:', item);
            console.log('item.originalData:', item.originalData);
            console.log('item.id:', item.id);

            // item 是从 MediaCard 传来的格式化后的数据，需要获取原始数据
            const batch = item.originalData || item;
            console.log('获取到的 batch:', batch);
            console.log('batch.id:', batch.id);

            // 修复验证逻辑：确保 batch 存在且有有效的 id
            let batchId = null;
            if (batch && batch.id) {
                batchId = batch.id;
            } else if (item && item.id) {
                batchId = item.id;
            }

            console.log('最终使用的 batchId:', batchId);

            if (!batchId) {
                console.error('无效的批次数据，无法跳转', { item, batch, batchId });
                uni.showToast({
                    title: '无效的批次数据',
                    icon: 'none'
                });
                return;
            }

            const url = `/pages/admin/media/batch-detail?id=${batchId}`;
            console.log('跳转URL:', url);

            uni.navigateTo({
                url: url,
                fail: (err) => {
                    console.error('跳转到批次详情页失败:', err);
                    uni.showToast({
                        title: '页面跳转失败',
                        icon: 'none'
                    });
                }
            });
        },

        // 格式化批次项数据为MediaCard组件需要的格式
        formatBatchItem (batch) {
            const metaItems = [
                {
                    icon: '📅',
                    text: this.formatDateRange(batch.startTime, batch.endTime)
                },
                {
                    icon: '#',
                    text: batch.batchId
                },
                {
                    icon: '👥',
                    text: `${batch.participants || 0}人参与`
                },
                {
                    icon: '💰',
                    text: `¥${batch.totalReward || 0}`
                }
            ];

            // 获取缩略图和时长
            let thumbnail = '';
            let duration = '';
            if (batch.videos && batch.videos.length > 0 && batch.videos[0].cover) {
                thumbnail = batch.videos[0].cover;
                if (batch.videos[0].duration) {
                    duration = this.formatDuration(batch.videos[0].duration);
                }
            }

            return {
                id: batch.id,
                title: batch.title,
                description: batch.description,
                thumbnail: thumbnail,
                duration: duration,
                statusText: this.getBatchStatusText(batch),
                statusType: this.getBatchStatusType(batch),
                metaItems: metaItems,
                originalData: batch
            };
        },

        createNewBatch () {
            uni.showToast({
                title: '请先选择视频创建批次',
                icon: 'none'
            });
        },

        getCurrentStatusLabel () {
            const currentTab = this.batchStatusTabs[this.currentTabIndex];
            return currentTab.name === '全部' ? '' : currentTab.name;
        },

        onImageLoad (e) {
            console.log('图片加载成功:', e);
        },

        onImageError (e) {
            console.log('图片加载失败:', e);
        }
    }
}
</script>

<style lang="scss" scoped>
@import '@/styles/index.scss';

.container {
    padding: 0;
    background-color: #f7f7f7;
}

/* 选项卡容器 */
.tabs-container {
    background-color: #fff;
    padding: 0 20rpx;
    border-bottom: 1rpx solid #f0f0f0;
}

/* 自定义选项卡样式 */
.custom-tabs {
    display: flex;
    justify-content: space-around;
    padding: 20rpx 0;
}

.custom-tab {
    flex: 1;
    text-align: center;
    padding: 16rpx 0;
    font-size: 28rpx;
    color: #666666;
    position: relative;
    transition: all 0.3s ease;
}

.custom-tab.active {
    color: #007AFF;
    font-weight: 600;
}

.custom-tab.active::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 40rpx;
    height: 4rpx;
    background-color: #007AFF;
    border-radius: 2rpx;
}

/* 媒体列表容器 */
.media-list-container {
    padding: 20rpx;
    padding-top: 0;
}

/* 样式现在由 MediaCard 组件提供 */
</style>
